import { client, clientWithToken } from "@/sanity/lib/client";
import {
  sendEmailsToSubscribers,
  notifyIfHighFailureRate,
} from "../email/newsletter/bulk";
import { groq } from "next-sanity";
import type { Newsletter, Subscriber } from "@/sanity/types";

export type SendNewslettersResult = {
  success: boolean;
  sentCount?: number;
  error?: string;
};

export async function getNewsletterById(
  newsletterId: string,
): Promise<Newsletter | null> {
  const getNewsletterQuery = groq`*[_type == "newsletter" && _id == $newsletterId][0]`;
  return await client.fetch(getNewsletterQuery, { newsletterId });
}

export async function getActiveSubscribers(): Promise<Subscriber[]> {
  const getSubscribersQuery = groq`*[_type == "subscriber" && status == "active"]`;
  return await client.fetch(getSubscribersQuery);
}

export async function updateNewsletterStatus(
  newsletterId: string,
): Promise<void> {
  await clientWithToken
    .patch(newsletterId)
    .set({
      status: "sent",
      sentAt: new Date().toISOString(),
    })
    .commit();
}

export async function sendNewsletters(
  newsletterId: string,
): Promise<SendNewslettersResult> {
  try {
    const newsletter = await getNewsletterById(newsletterId);

    if (!newsletter) {
      return {
        success: false,
        error: "Newsletter não encontrada",
      };
    }

    if (newsletter.status === "sent") {
      return {
        success: false,
        error: "Esta newsletter já foi enviada",
      };
    }

    const subscribers = await getActiveSubscribers();

    if (subscribers.length === 0) {
      return {
        success: false,
        error: "Nenhum assinante ativo encontrado",
      };
    }

    const { sentCount, failedEmails, stopped } = await sendEmailsToSubscribers(
      newsletter,
      subscribers,
    );

    // Only update status if sending wasn't stopped due to high failure rate
    if (!stopped) {
      await updateNewsletterStatus(newsletterId);

      console.log(
        `📊 Newsletter sending completed: ${sentCount}/${subscribers.length} emails sent successfully`,
      );

      if (failedEmails.length > 0) {
        const failureRate = Math.round(
          (failedEmails.length / subscribers.length) * 100,
        );
        console.warn(
          `⚠️ Failed to send to ${failedEmails.length} emails (${failureRate}%):`,
          failedEmails,
        );

        // Notify support if failure rate is high but below stopping threshold (5-9%)
        await notifyIfHighFailureRate(
          newsletter,
          subscribers.length,
          sentCount,
          failedEmails,
        );
      }
    } else {
      console.error(
        `🚨 Newsletter sending STOPPED due to high failure rate: ${sentCount}/${subscribers.length} sent, ${failedEmails.length} failed`,
      );
    }

    return {
      success: !stopped,
      sentCount,
      error: stopped
        ? `Sending stopped due to high failure rate (${Math.round((failedEmails.length / subscribers.length) * 100)}%)`
        : undefined,
    };
  } catch (error) {
    console.error("Error sending newsletters:", error);
    return {
      success: false,
      error: "Erro interno do servidor ao enviar newsletters",
    };
  }
}

import { createTransporter, getSupportEmail } from "./index";
import type { Newsletter } from "@/sanity/types";

export type NotificationData = {
  totalSubscribers: number;
  sentCount: number;
  failedCount: number;
  failedEmails: string[];
  stoppedEarly: boolean;
};

export async function sendSupportNotification(
  newsletter: Newsletter,
  data: NotificationData,
): Promise<void> {
  try {
    const transporter = createTransporter();
    const supportEmail = getSupportEmail();
    const failureRate = Math.round(
      (data.failedCount / data.totalSubscribers) * 100,
    );

    const subject = `🚨 Newsletter Sending ${data.stoppedEarly ? "STOPPED" : "COMPLETED"} - High Failure Rate (${failureRate}%)`;

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Newsletter Alert</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .alert { background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .stats { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .failed-emails { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; }
          .failed-emails ul { max-height: 200px; overflow-y: auto; }
        </style>
      </head>
      <body>
        <h2>🚨 Newsletter Alert - High Failure Rate</h2>
        
        <div class="alert">
          <strong>Status:</strong> ${data.stoppedEarly ? "EXECUTION STOPPED" : "COMPLETED WITH ISSUES"}<br>
          <strong>Reason:</strong> ${failureRate}% of emails failed (threshold: 10%)
        </div>

        <div class="stats">
          <h3>📊 Statistics</h3>
          <ul>
            <li><strong>Newsletter:</strong> ${newsletter.subject}</li>
            <li><strong>Total Subscribers:</strong> ${data.totalSubscribers}</li>
            <li><strong>Successfully Sent:</strong> ${data.sentCount}</li>
            <li><strong>Failed:</strong> ${data.failedCount}</li>
            <li><strong>Failure Rate:</strong> ${failureRate}%</li>
            <li><strong>Execution Status:</strong> ${data.stoppedEarly ? "Stopped Early" : "Completed"}</li>
          </ul>
        </div>

        <div class="failed-emails">
          <h3>❌ Failed Email Addresses</h3>
          <ul>
            ${data.failedEmails.map((email) => `<li>${email}</li>`).join("")}
          </ul>
        </div>

        <p><strong>Action Required:</strong> Please investigate the email delivery issues and consider re-sending to failed addresses after resolving the problem.</p>
        
        <p>Time: ${new Date().toISOString()}</p>
      </body>
      </html>
    `;

    await transporter.sendMail({
      from: {
        name: "Pedro Yaba Newsletter System",
        address: supportEmail,
      },
      to: supportEmail,
      subject,
      html: htmlContent,
      text: `
        Newsletter Alert - High Failure Rate

        Status: ${data.stoppedEarly ? "EXECUTION STOPPED" : "COMPLETED WITH ISSUES"}
        Newsletter: ${newsletter.subject}
        Total Subscribers: ${data.totalSubscribers}
        Successfully Sent: ${data.sentCount}
        Failed: ${data.failedCount}
        Failure Rate: ${failureRate}%

        Failed Emails:
        ${data.failedEmails.join("\n")}

        Time: ${new Date().toISOString()}
      `,
    });

    console.log(`📧 Support notification sent to: ${supportEmail}`);
  } catch (error) {
    console.error("❌ Failed to send support notification:", error);
  }
}

import { defineType, defineField } from "sanity";

export const newsletterStatsType = defineType({
  name: "newsletterStats",
  title: "Estatísticas da Newsletter",
  type: "document",
  fields: [
    defineField({
      name: "newsletter",
      title: "Newsletter",
      type: "reference",
      to: [{ type: "newsletter" }],
      description: "Newsletter associada a estas estatísticas",
      validation: (Rule) => Rule.required().error("A newsletter é obrigatória"),
    }),
    defineField({
      name: "totalSubscribers",
      title: "Total de Assinantes",
      type: "number",
      description: "Número total de assinantes no momento do envio",
      validation: (Rule) =>
        Rule.required()
          .min(0)
          .error("O total de assinantes deve ser um número positivo"),
    }),
    defineField({
      name: "successCount",
      title: "Envios Bem-sucedidos",
      type: "number",
      description: "Número de emails enviados com sucesso",
      validation: (Rule) =>
        Rule.required()
          .min(0)
          .error("O número de sucessos deve ser um número positivo"),
    }),
    defineField({
      name: "failedCount",
      title: "Envios Falhados",
      type: "number",
      description: "Número de emails que falharam no envio",
      validation: (Rule) =>
        Rule.required()
          .min(0)
          .error("O número de falhas deve ser um número positivo"),
    }),
    defineField({
      name: "failedEmails",
      title: "Emails Falhados",
      type: "array",
      of: [{ type: "string" }],
      description: "Lista dos emails que falharam no envio",
      validation: (Rule) => Rule.unique().error("Emails duplicados não são permitidos"),
    }),
    defineField({
      name: "failureRate",
      title: "Taxa de Falha (%)",
      type: "number",
      description: "Percentual de falhas no envio",
      validation: (Rule) =>
        Rule.min(0)
          .max(100)
          .error("A taxa de falha deve estar entre 0 e 100%"),
    }),
    defineField({
      name: "sendingStartedAt",
      title: "Início do Envio",
      type: "datetime",
      description: "Data e hora em que o envio foi iniciado",
      validation: (Rule) => Rule.required().error("A data de início é obrigatória"),
      options: {
        dateFormat: "DD/MM/YYYY",
        timeFormat: "HH:mm:ss",
      },
    }),
    defineField({
      name: "sendingCompletedAt",
      title: "Fim do Envio",
      type: "datetime",
      description: "Data e hora em que o envio foi concluído",
      options: {
        dateFormat: "DD/MM/YYYY",
        timeFormat: "HH:mm:ss",
      },
    }),
    defineField({
      name: "wasStopped",
      title: "Envio Interrompido",
      type: "boolean",
      description: "Indica se o envio foi interrompido devido a alta taxa de falha",
      initialValue: false,
    }),
    defineField({
      name: "stopReason",
      title: "Motivo da Interrupção",
      type: "text",
      description: "Motivo pelo qual o envio foi interrompido",
      hidden: ({ document }) => !document?.wasStopped,
      rows: 3,
    }),
  ],
  preview: {
    select: {
      newsletterSubject: "newsletter.subject",
      successCount: "successCount",
      totalSubscribers: "totalSubscribers",
      failureRate: "failureRate",
      wasStopped: "wasStopped",
    },
    prepare(selection) {
      const { newsletterSubject, successCount, totalSubscribers, failureRate, wasStopped } = selection;
      const title = newsletterSubject || "Newsletter sem assunto";
      const successRate = totalSubscribers > 0 ? Math.round((successCount / totalSubscribers) * 100) : 0;
      const status = wasStopped ? "INTERROMPIDO" : "CONCLUÍDO";
      
      return {
        title: `${title} - ${status}`,
        subtitle: `${successCount}/${totalSubscribers} enviados (${successRate}% sucesso, ${failureRate || 0}% falha)`,
        media: wasStopped ? "⚠️" : successRate >= 95 ? "✅" : successRate >= 90 ? "⚡" : "❌",
      };
    },
  },
  orderings: [
    {
      title: "Mais recente primeiro",
      name: "createdDesc",
      by: [{ field: "_createdAt", direction: "desc" }],
    },
    {
      title: "Por taxa de sucesso",
      name: "successRateDesc",
      by: [{ field: "failureRate", direction: "asc" }],
    },
    {
      title: "Por newsletter",
      name: "newsletterAsc",
      by: [{ field: "newsletter.subject", direction: "asc" }],
    },
  ],
});

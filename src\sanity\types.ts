/**
 * ---------------------------------------------------------------------------------
 * This file has been generated by Sanity TypeGen.
 * Command: `sanity typegen generate`
 *
 * Any modifications made directly to this file will be overwritten the next time
 * the TypeScript definitions are generated. Please make changes to the Sanity
 * schema definitions and/or GROQ queries if you need to update these types.
 *
 * For more information on how to use Sanity TypeGen, visit the official documentation:
 * https://www.sanity.io/docs/sanity-typegen
 * ---------------------------------------------------------------------------------
 */

// Source: schema.json
export type Subscriber = {
  _id: string;
  _type: "subscriber";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  email: string;
  status: "active" | "unsubscribed";
  subscribedAt?: string;
  unsubscribedAt?: string;
  unsubscribeToken: string;
};

export type Newsletter = {
  _id: string;
  _type: "newsletter";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  subject: string;
  body: Array<
    | {
        children?: Array<{
          marks?: Array<string>;
          text?: string;
          _type: "span";
          _key: string;
        }>;
        style?: "normal" | "h1" | "h2" | "h3" | "h4" | "blockquote";
        listItem?: "bullet";
        markDefs?: Array<{
          href?: string;
          _type: "link";
          _key: string;
        }>;
        level?: number;
        _type: "block";
        _key: string;
      }
    | {
        asset?: {
          _ref: string;
          _type: "reference";
          _weak?: boolean;
          [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
        };
        media?: unknown;
        hotspot?: SanityImageHotspot;
        crop?: SanityImageCrop;
        alt?: string;
        _type: "image";
        _key: string;
      }
  >;
  status: "pending" | "sent";
  sentAt?: string;
};

export type NewsletterStats = {
  _id: string;
  _type: "newsletterStats";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  newsletter: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "newsletter";
  };
  totalSubscribers: number;
  successCount: number;
  failedCount: number;
  failedEmails?: string[];
  failureRate?: number;
  sendingStartedAt: string;
  sendingCompletedAt?: string;
  wasStopped?: boolean;
  stopReason?: string;
};

export type BlockContent = Array<
  | {
      children?: Array<{
        marks?: Array<string>;
        text?: string;
        _type: "span";
        _key: string;
      }>;
      style?: "normal" | "h1" | "h2" | "h3" | "h4" | "blockquote";
      listItem?: "bullet";
      markDefs?: Array<{
        href?: string;
        _type: "link";
        _key: string;
      }>;
      level?: number;
      _type: "block";
      _key: string;
    }
  | {
      asset?: {
        _ref: string;
        _type: "reference";
        _weak?: boolean;
        [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
      };
      media?: unknown;
      hotspot?: SanityImageHotspot;
      crop?: SanityImageCrop;
      alt?: string;
      _type: "image";
      _key: string;
    }
>;

export type SanityImagePaletteSwatch = {
  _type: "sanity.imagePaletteSwatch";
  background?: string;
  foreground?: string;
  population?: number;
  title?: string;
};

export type SanityImagePalette = {
  _type: "sanity.imagePalette";
  darkMuted?: SanityImagePaletteSwatch;
  lightVibrant?: SanityImagePaletteSwatch;
  darkVibrant?: SanityImagePaletteSwatch;
  vibrant?: SanityImagePaletteSwatch;
  dominant?: SanityImagePaletteSwatch;
  lightMuted?: SanityImagePaletteSwatch;
  muted?: SanityImagePaletteSwatch;
};

export type SanityImageDimensions = {
  _type: "sanity.imageDimensions";
  height: number;
  width: number;
  aspectRatio: number;
};

export type SanityImageHotspot = {
  _type: "sanity.imageHotspot";
  x: number;
  y: number;
  height: number;
  width: number;
};

export type SanityImageCrop = {
  _type: "sanity.imageCrop";
  top: number;
  bottom: number;
  left: number;
  right: number;
};

export type SanityFileAsset = {
  _id: string;
  _type: "sanity.fileAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  source?: SanityAssetSourceData;
};

export type SanityImageAsset = {
  _id: string;
  _type: "sanity.imageAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  metadata?: SanityImageMetadata;
  source?: SanityAssetSourceData;
};

export type SanityImageMetadata = {
  _type: "sanity.imageMetadata";
  location?: Geopoint;
  dimensions?: SanityImageDimensions;
  palette?: SanityImagePalette;
  lqip?: string;
  blurHash?: string;
  hasAlpha?: boolean;
  isOpaque?: boolean;
};

export type Geopoint = {
  _type: "geopoint";
  lat?: number;
  lng?: number;
  alt?: number;
};

export type Slug = {
  _type: "slug";
  current: string;
  source?: string;
};

export type SanityAssetSourceData = {
  _type: "sanity.assetSourceData";
  name?: string;
  id?: string;
  url?: string;
};

export type AllSanitySchemaTypes =
  | Subscriber
  | Newsletter
  | BlockContent
  | SanityImagePaletteSwatch
  | SanityImagePalette
  | SanityImageDimensions
  | SanityImageHotspot
  | SanityImageCrop
  | SanityFileAsset
  | SanityImageAsset
  | SanityImageMetadata
  | Geopoint
  | Slug
  | SanityAssetSourceData;
export declare const internalGroqTypeReferenceTo: unique symbol;
// Source: ./src/app/[locale]/(site)/actions/newsletter.ts
// Variable: findSubscriberQuery
// Query: *[_type == "subscriber" && email == $email][0]
export type FindSubscriberQueryResult = {
  _id: string;
  _type: "subscriber";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  email: string;
  status: "active" | "unsubscribed";
  subscribedAt?: string;
  unsubscribedAt?: string;
  unsubscribeToken: string;
} | null;
// Variable: findSubscriberByUnsubscribeTokenQuery
// Query: *[_type == "subscriber" && unsubscribeToken == $token][0]
export type FindSubscriberByUnsubscribeTokenQueryResult = {
  _id: string;
  _type: "subscriber";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  email: string;
  status: "active" | "unsubscribed";
  subscribedAt?: string;
  unsubscribedAt?: string;
  unsubscribeToken: string;
} | null;

// Source: ./src/sanity/lib/queries.ts
// Variable: POSTS_QUERY
// Query: *[_type == "post"]{  _id, title, embed, type}
export type POSTS_QUERYResult = Array<never>;

// Query TypeMap
import "@sanity/client";
// @ts-expect-error
declare module "@sanity/client" {
  interface SanityQueries {
    '*[_type == "subscriber" && email == $email][0]': FindSubscriberQueryResult;
    '*[_type == "subscriber" && unsubscribeToken == $token][0]': FindSubscriberByUnsubscribeTokenQueryResult;
    '*[_type == "post"]{\n  _id, title, embed, type\n}': POSTS_QUERYResult;
  }
}
